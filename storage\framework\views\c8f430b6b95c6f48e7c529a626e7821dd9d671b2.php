<!DOCTYPE html>
<html lang="en" data-layout="vertical" data-topbar="light" data-sidebar="dark" data-sidebar-size="lg" data-sidebar-image="none" data-preloader="disable" data-theme="default" data-theme-colors="default">

<!-- Mirrored from themesbrand.com/velzon/html/master/index.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 23 Aug 2024 16:35:21 GMT -->

<head>

    <meta charset="utf-8" />
    <title><?php echo $__env->yieldContent('title', 'Dashboard Admission Pendaftaran Online'); ?></title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
    <meta content="Themesbrand" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="<?php echo e(asset('assets/images/favicon.ico')); ?>">
    <link href="<?php echo e(asset('assets/libs/jsvectormap/css/jsvectormap.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/libs/swiper/swiper-bundle.min.css')); ?>" rel="stylesheet" type="text/css" />
    <script src="<?php echo e(asset('assets/js/layout.js')); ?>"></script>
    <link href="<?php echo e(asset('assets/css/bootstrap.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/icons.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/app.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom.min.css')); ?>" rel="stylesheet" type="text/css" />
    <!-- Font Awesome dari CDN -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.min.css" />
    
    <!-- DataTables CSS -->
    <link href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    <link href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.bootstrap5.min.css" rel="stylesheet" type="text/css" />
    
    <!-- jQuery -->
    <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.6.0/jquery.min.js"></script>
    <!-- DataTables JS -->
    <script src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
    <script src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/responsive.bootstrap5.min.js"></script>
    
    <?php echo $__env->yieldPushContent('styles'); ?>

    <!-- Custom CSS untuk Hamburger Menu -->
    <style>
        /* Hamburger Icon Animation */
        .hamburger-icon {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            width: 20px;
            height: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .hamburger-icon span {
            display: block;
            height: 2px;
            width: 100%;
            background-color: currentColor;
            transition: all 0.3s ease;
            transform-origin: center;
        }

        .hamburger-icon.open span:nth-child(1) {
            transform: rotate(45deg) translate(5px, 5px);
        }

        .hamburger-icon.open span:nth-child(2) {
            opacity: 0;
        }

        .hamburger-icon.open span:nth-child(3) {
            transform: rotate(-45deg) translate(7px, -6px);
        }

        /* Sidebar Toggle Animation */
        .app-menu {
            transition: all 0.3s ease;
        }

        /* Mobile: Show/Hide sidebar */
        @media (max-width: 767px) {
            .app-menu {
                position: fixed;
                left: -250px;
                z-index: 1000;
                transition: left 0.3s ease;
            }

            .vertical-sidebar-enable .app-menu {
                left: 0;
            }

            .vertical-overlay {
                display: none;
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                z-index: 999;
            }

            .vertical-sidebar-enable .vertical-overlay {
                display: block;
            }
        }

        /* Desktop: Sidebar size toggle */
        @media (min-width: 768px) {
            [data-sidebar-size="sm"] .app-menu {
                width: 70px;
            }

            [data-sidebar-size="sm"] .app-menu .navbar-nav .nav-link span {
                display: none;
            }

            [data-sidebar-size="lg"] .app-menu {
                width: 250px;
            }
        }
    </style>
</head>
<style>
    .navbar-brand-box{
        margin-top: 1rem;
    }
    /* Perbaikan style DataTables */
    .dataTables_wrapper .dataTables_paginate .paginate_button {
        padding: 0.3em 0.8em;
        margin-left: 2px;
        border-radius: 4px;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button.current {
        background: #405189;
        color: white !important;
        border: 1px solid #405189;
    }
    .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
        background: #f3f6f9;
        color: #405189 !important;
        border: 1px solid #f3f6f9;
    }
    .dataTables_wrapper .dataTables_length, 
    .dataTables_wrapper .dataTables_filter, 
    .dataTables_wrapper .dataTables_info, 
    .dataTables_wrapper .dataTables_processing, 
    .dataTables_wrapper .dataTables_paginate {
        margin-bottom: 10px;
        color: #495057;
    }
</style>

<body>
    <!-- Begin page -->
    <div id="layout-wrapper">

        <?php echo $__env->make('layouts.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        <?php echo $__env->make('layouts.sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

        <!-- Content Wrapper. Contains page content -->
        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
            </div>
            
            <?php echo $__env->make('layouts.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
        </div>
    </div>

    <!-- JAVASCRIPT -->
    <script src="<?php echo e(asset('assets/libs/bootstrap/js/bootstrap.bundle.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/simplebar/simplebar.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/node-waves/waves.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/feather-icons/feather.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/pages/plugins/lord-icon-2.1.0.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/plugins.js')); ?>"></script>

    <!-- apexcharts -->
    <script src="<?php echo e(asset('assets/libs/apexcharts/apexcharts.min.js')); ?>"></script>

    <!-- Vector map-->
    <script src="<?php echo e(asset('assets/libs/jsvectormap/js/jsvectormap.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/libs/jsvectormap/maps/world-merc.js')); ?>"></script>

    <!--Swiper slider js-->
    <script src="<?php echo e(asset('assets/libs/swiper/swiper-bundle.min.js')); ?>"></script>

    <!-- Dashboard init -->
    <script src="<?php echo e(asset('assets/js/pages/dashboard-ecommerce.init.js')); ?>"></script>

    <!-- App js -->
    <script src="<?php echo e(asset('assets/js/app.js')); ?>"></script>

    <?php echo $__env->yieldPushContent('scripts'); ?>

    <!-- Custom Script untuk Hamburger Menu -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Pastikan tombol hamburger berfungsi
            const hamburgerBtn = document.getElementById('topnav-hamburger-icon');
            if (hamburgerBtn) {
                hamburgerBtn.addEventListener('click', function(e) {
                    e.preventDefault();
                    console.log('Hamburger clicked!'); // Debug

                    // Toggle sidebar
                    const body = document.body;
                    const hamburgerIcon = document.querySelector('.hamburger-icon');
                    const layoutWrapper = document.getElementById('layout-wrapper');

                    // Cek ukuran layar
                    const screenWidth = window.innerWidth;

                    if (screenWidth <= 767) {
                        // Mobile: Toggle sidebar visibility
                        body.classList.toggle('vertical-sidebar-enable');
                    } else if (screenWidth <= 1024) {
                        // Tablet: Toggle sidebar size
                        const currentSize = document.documentElement.getAttribute('data-sidebar-size');
                        if (currentSize === 'sm') {
                            document.documentElement.setAttribute('data-sidebar-size', 'lg');
                        } else {
                            document.documentElement.setAttribute('data-sidebar-size', 'sm');
                        }
                    } else {
                        // Desktop: Toggle sidebar size
                        const currentSize = document.documentElement.getAttribute('data-sidebar-size');
                        if (currentSize === 'sm') {
                            document.documentElement.setAttribute('data-sidebar-size', 'lg');
                        } else {
                            document.documentElement.setAttribute('data-sidebar-size', 'sm');
                        }
                    }

                    // Toggle hamburger icon animation
                    if (hamburgerIcon) {
                        hamburgerIcon.classList.toggle('open');
                    }
                });
            }

            // Close sidebar when clicking overlay (mobile)
            const overlay = document.querySelector('.vertical-overlay');
            if (overlay) {
                overlay.addEventListener('click', function() {
                    document.body.classList.remove('vertical-sidebar-enable');
                    const hamburgerIcon = document.querySelector('.hamburger-icon');
                    if (hamburgerIcon) {
                        hamburgerIcon.classList.remove('open');
                    }
                });
            }
        });
    </script>
</body>

<!-- Mirrored from themesbrand.com/velzon/html/master/index.html by HTTrack Website Copier/3.x [XR&CO'2014], Fri, 23 Aug 2024 16:36:17 GMT -->

</html>
<?php /**PATH C:\laragon\www\regkan\resources\views/layouts/app.blade.php ENDPATH**/ ?>